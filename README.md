# Simple CLI Application

A simple CLI application built with TypeScript and Oclif.

## Features

- ✅ TypeScript configuration with strict type checking
- ✅ Oclif-powered CLI framework
- ✅ Simple "Hello World" command
- ✅ Development and production build scripts
- ✅ Clean project structure
- ✅ Easy to extend with new commands

## Project Structure

```
algotrade-framework/
├── src/
│   ├── index.ts          # CLI entry point
│   └── commands/         # C<PERSON>I commands
│       └── hello.ts      # Hello world command
├── dist/                 # Compiled JavaScript output (generated)
├── package.json          # Project dependencies and scripts
├── tsconfig.json         # TypeScript configuration
└── README.md            # This file
```

## Getting Started

### Prerequisites

- Node.js (version 14 or higher)
- npm or yarn

### Installation

1. Install dependencies:
   ```bash
   npm install
   ```

### CLI Usage

1. **Show available commands**:
   ```bash
   npm run cli -- --help
   ```

2. **Run the hello command**:
   ```bash
   npm run cli -- hello
   ```

3. **Say hello with custom name**:
   ```bash
   npm run cli -- hello --name John
   ```

4. **Get help for a specific command**:
   ```bash
   npm run cli -- hello --help
   ```

### Development

1. **Build the project**:
   ```bash
   npm run build
   ```

2. **Run directly**:
   ```bash
   node dist/index.js --help
   ```

### Other Commands

- **Clean build directory**:
  ```bash
  npm run clean
  ```

## CLI Commands

The CLI includes a simple hello world command:

### Hello Command (Default)
- Simple greeting message
- Accepts `--name` flag to customize the greeting
- Demonstrates basic Oclif command structure
- Shows help system integration

## TypeScript Configuration

The project uses strict TypeScript settings including:

- Strict null checks
- No implicit any
- Strict function types
- No implicit returns
- And more strict type checking options

## Adding New Commands

To add new commands to your CLI:

1. **Create a new command file** in `src/commands/`:
   ```bash
   # Example: src/commands/goodbye.ts
   ```

2. **Use the Oclif command structure**:
   ```typescript
   import { Command, Flags } from '@oclif/core';

   export default class Goodbye extends Command {
     static override description = 'Say goodbye';

     static override flags = {
       help: Flags.help({ char: 'h' }),
       name: Flags.string({ char: 'n', description: 'name to say goodbye to' }),
     };

     async run(): Promise<void> {
       const { flags } = await this.parse(Goodbye);
       this.log(`Goodbye ${flags.name || 'world'}!`);
     }
   }
   ```

3. **Build and test**:
   ```bash
   npm run build
   npm run cli -- goodbye --name John
   ```

## Next Steps

You can extend this CLI by:

1. **Adding more commands**: Create new command files as needed
2. **Testing**: Add unit tests with Jest or Mocha
3. **Linting**: Set up ESLint for code quality
4. **Publishing**: Publish to npm for global installation
5. **Interactive Mode**: Add interactive prompts with inquirer
6. **Configuration**: Add config files and settings
7. **Plugins**: Add Oclif plugins for additional functionality

## Global Installation

To install globally and use `algotrade` command anywhere:

```bash
npm install -g .
algotrade --help
```

Happy coding! 🚀
