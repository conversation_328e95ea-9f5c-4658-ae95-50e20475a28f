
export function simpleMovingAverage(prices: number[], period: number): number[] {
  if (period <= 0 || period > prices.length) {
    throw new Error("Invalid period for simple moving average");
  }
  const sma: number[] = [];
  for (let i = 0; i < prices.length; i++) {
    if (i < period - 1) {
      sma.push(NaN);
    } else {
      const sum = prices.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
      sma.push(sum / period);
    }
  }
  return sma;
}