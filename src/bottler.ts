import { Interval } from "./interface";
import { <PERSON><PERSON>roker } from "./interface/IBroker";
import { Quote, OHLC, Instrument, MarketTiming } from "./models";
import EventEmitter from "events";

// ---------------- Types ----------------

// Add a mode to distinguish between live and backtest operations
export type BlotterMode = "live" | "backtest";

export interface BlotterOptions {
  intervals: Interval[];
  instruments: string[];
  mode: BlotterMode; // Mandatory mode setting
  preloadDays?: number; // Used for live trading context
  barLookbackWindow?: number;
}

// Internal state for managing bar construction from ticks
interface LiveBarState {
  ticks: Quote[];
  timer?: NodeJS.Timeout; // Only used in live mode
}

interface BlotterEvents extends Record<string | symbol, (...args: any[]) => void> {
  tick: (tick: Quote) => void;
  bar: (symbol: string, interval: Interval, bar: OHLC) => void;
  backtestComplete: () => void; // Event to signal the end of a backtest
}

// ---------------- Typed EventEmitter ----------------
class TypedEventEmitter<Events extends Record<string | symbol, (...args: any[]) => void>> extends EventEmitter {
  override on<K extends keyof Events>(event: K, listener: Events[K]): this {
    return super.on(event as string | symbol, listener as (...args: any[]) => void);
  }

  override emit<K extends keyof Events>(
    event: K,
    ...args: Events[K] extends (...args: infer P) => void ? P : never
  ): boolean {
    return super.emit(event as string | symbol, ...args);
  }

  override off<K extends keyof Events>(event: K, listener: Events[K]): this {
    return super.off(event as string | symbol, listener as (...args: any[]) => void);
  }

  override once<K extends keyof Events>(event: K, listener: Events[K]): this {
    return super.once(event as string | symbol, listener as (...args: any[]) => void);
  }
}

// ---------------- Blotter ----------------
export class Blotter extends TypedEventEmitter<BlotterEvents> {
  private broker: IBroker;
  private intervals: Interval[];
  private instruments: Instrument[];
  private preloadDays: number;
  private barLookbackWindow: number;
  private mode: BlotterMode;

  // State for live trading (manages ticks and timers)
  private liveTickBuffer: Map<string, LiveBarState> = new Map();

  // Unified data store for both live and backtest bars
  private bars: Map<string, Map<Interval, OHLC[]>> = new Map();

  constructor(broker: IBroker, options: BlotterOptions) {
    super();
    this.broker = broker;
    this.intervals = options.intervals;
    this.mode = options.mode;
    // Handle both "NSE:RELIANCE" and "RELIANCE" formats
    this.instruments = broker.getInstruments().filter((i) => {
      return options.instruments.some(requestedSymbol => {
        // If requested symbol has exchange prefix (e.g., "NSE:RELIANCE")
        if (requestedSymbol.includes(':')) {
          const [exchange, symbol] = requestedSymbol.split(':');
          return i.exchange === exchange && i.symbol === symbol;
        }
        // Otherwise, match directly
        return i.symbol === requestedSymbol;
      });
    });

    if (this.instruments.length === 0) {
      console.warn(`[Blotter] No instruments found for symbols: ${options.instruments.join(', ')}`);
    }
    this.preloadDays = options.preloadDays || 0;
    this.barLookbackWindow = options.barLookbackWindow || 1000; // Default to 1000 bars

    // Initialize data structures for all instruments
    for (const inst of this.instruments) {
      this.liveTickBuffer.set(inst.symbol, { ticks: [] });
      const intervalMap = new Map<Interval, OHLC[]>();
      this.intervals.forEach((iv) => intervalMap.set(iv, []));
      this.bars.set(inst.symbol, intervalMap);
    }
  }

  /**
     * Starts the blotter in LIVE mode.
     * Connects to the ticker, subscribes to ticks, and schedules bar creation based on wall-clock time.
     */
  public async start(): Promise<void> {
    if (this.mode !== "live") {
      console.warn("start() should only be called in 'live' mode. For backtesting, use runBacktest().");
      return;
    }

    const marketTiming = this.broker.getTiming();

    if (this.preloadDays > 0) {
      await this.preloadHistoricalBars(marketTiming);
    }

    await this.broker.connectTicker({
      onTicks: (ticks) => this.onLiveTicks(ticks),
    });

    const instrumentTokens = this.instruments.map((i) => i.instrumentToken as number);
    this.broker.subscribeTicks(instrumentTokens);

    for (const inst of this.instruments) {
      for (const interval of this.intervals) {
        this.scheduleNextBar(inst.symbol, interval, marketTiming);
      }
    }
  }

  /**
   * Runs the blotter in BACKTEST mode.
   * Fetches all historical data for the given period and simulates the market event by event.
   * @param from - The start date for the backtest.
   * @param to - The end date for the backtest.
   */
  public async runBacktest(from: Date, to: Date): Promise<void> {
    if (this.mode !== "backtest") {
      console.warn("runBacktest() should only be called in 'backtest' mode.");
      return;
    }

    // 1. Determine the most granular interval to use as the "source of truth" for the simulation.
    const baseInterval = this.getSmallestInterval();

    // 2. Fetch all historical data for the base interval for all instruments.
    const allBaseBars: OHLC[] = [];
    for (const inst of this.instruments) {
      const historical = await this.broker.getHistoricalData({
        instrumentToken: inst.instrumentToken,
        interval: baseInterval,
        from,
        to,
      });
      // Fix the symbol in the returned OHLC data to use the actual symbol instead of instrumentToken
      const correctedHistorical = historical.map(bar => ({
        ...bar,
        symbol: inst.symbol
      }));
      allBaseBars.push(...correctedHistorical);
    }

    // 3. Create a single, unified timeline of all events, sorted chronologically.
    const masterTimeline = allBaseBars.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    // 4. Prepare resampling buffers to build larger intervals from the base interval.
    const resampleBuffers = new Map<string, Map<Interval, OHLC[]>>();
    for (const inst of this.instruments) {
      const intervalMap = new Map<Interval, OHLC[]>();
      this.intervals.forEach(iv => intervalMap.set(iv, []));
      resampleBuffers.set(inst.symbol, intervalMap);
    }

    // 5. Main backtest loop: Iterate through the master timeline and simulate events.
    let barsEmitted = 0;
    for (const baseBar of masterTimeline) {
      const symbol = baseBar.symbol;

      // For every interval, check if this baseBar contributes to or completes a new bar.
      for (const interval of this.intervals) {
        const buffer = resampleBuffers.get(symbol)?.get(interval);
        if (!buffer) continue;

        buffer.push(baseBar);

        // Check if this baseBar completes the bar for the current interval
        if (this.isBarComplete(baseBar.timestamp, interval)) {
          const newBar = this.aggregateBarsToOHLC(buffer, symbol, baseBar.timestamp);

          const barHistory = this.bars.get(symbol)?.get(interval);
          if (barHistory) {
            barHistory.push(newBar);
            if (barHistory.length > this.barLookbackWindow) {
              barHistory.shift(); // Remove the oldest bar
            }
          }

          this.emit("bar", symbol, interval, newBar);
          barsEmitted++;

          // Clear the buffer for the next bar
          buffer.length = 0;
        }
      }
    }


    this.emit("backtestComplete");
  }

  // --- Live Mode Methods ---

  private onLiveTicks(ticks: Quote[]) {
    for (const tick of ticks) {
      const state = this.liveTickBuffer.get(tick.symbol);
      if (!state) continue;
      state.ticks.push(tick);
      this.emit("tick", tick);
    }
  }

  private scheduleNextBar(symbol: string, interval: Interval, marketTiming: MarketTiming) {
    const now = this.getCurrentTime(marketTiming.timezone);
    const { barEnd } = this.getAlignedBarTime(now, marketTiming.marketStartTime, interval);
    const delay = barEnd.getTime() - now.getTime();

    const state = this.liveTickBuffer.get(symbol);
    if (!state) return;

    state.timer = setTimeout(() => {
      this.emitLiveBar(symbol, interval);
      this.scheduleNextBar(symbol, interval, marketTiming);
    }, delay);
  }

  private emitLiveBar(symbol: string, interval: Interval) {
    const state = this.liveTickBuffer.get(symbol);
    if (!state || state.ticks.length === 0) return;

    const ticks = state.ticks;
    const ohlc: OHLC = {
      symbol,
      open: ticks[0]!.lastPrice,
      high: Math.max(...ticks.map((t) => t.lastPrice)),
      low: Math.min(...ticks.map((t) => t.lastPrice)),
      close: ticks[ticks.length - 1]!.lastPrice,
      timestamp: new Date(),
    };

    const barHistory = this.bars.get(symbol)?.get(interval);
    if (barHistory) {
      barHistory.push(ohlc);
      if (barHistory.length > this.barLookbackWindow) {
        barHistory.shift(); // Remove the oldest bar
      }
    }

    this.emit("bar", symbol, interval, ohlc);

    state.ticks = [];
  }

  // --- Shared & Utility Methods ---

  private async preloadHistoricalBars(marketTiming: MarketTiming) {
    const from = new Date();
    from.setDate(from.getDate() - this.preloadDays);
    const to = new Date();

    for (const inst of this.instruments) {
      for (const interval of this.intervals) {
        const historical = await this.broker.getHistoricalData({
          instrumentToken: inst.instrumentToken,
          interval,
          from,
          to,
        });
        this.bars.get(inst.symbol)?.get(interval)?.push(...historical);
      }
    }
  }

  public stop() {
    if (this.mode === 'live') {
      for (const state of this.liveTickBuffer.values()) {
        if (state.timer) clearTimeout(state.timer);
      }
      this.broker.disconnectTicker();
    }
  }

  public getBars(symbol: string, interval: Interval): OHLC[] {
    // Handle both "NSE:RELIANCE" and "RELIANCE" formats
    let actualSymbol = symbol;
    if (symbol.includes(':')) {
      const parts = symbol.split(':');
      actualSymbol = parts[1] || symbol;
    }
    return this.bars.get(actualSymbol)?.get(interval) || [];
  }

  // --- Backtest Helper Methods ---

  private aggregateBarsToOHLC(constituentBars: OHLC[], symbol: string, timestamp: Date): OHLC {
    if (constituentBars.length === 0) throw new Error("Cannot aggregate empty bars array.");

    return {
      symbol,
      timestamp,
      open: constituentBars[0]!.open,
      high: Math.max(...constituentBars.map(b => b.high)),
      low: Math.min(...constituentBars.map(b => b.low)),
      close: constituentBars[constituentBars.length - 1]!.close,
    };
  }

  private isBarComplete(timestamp: Date, interval: Interval): boolean {
    const intervalMinutes = this.convertIntervalToMinutes(interval);
    // Historical data timestamps represent the END of the bar period
    // So every timestamp from historical data represents a completed bar
    // For backtesting, we treat every base interval bar as complete
    // The resampling logic will handle aggregating them into larger intervals

    // For the base interval (smallest), every bar is complete
    const baseInterval = this.getSmallestInterval();
    if (interval === baseInterval) {
      return true;
    }

    // For larger intervals, check if timestamp aligns with interval boundaries
    const minutes = timestamp.getMinutes();
    const hours = timestamp.getHours();

    if (intervalMinutes < 60) {
      return minutes % intervalMinutes === 0;
    } else {
      const intervalHours = intervalMinutes / 60;
      return hours % intervalHours === 0 && minutes === 0;
    }
  }

  private getSmallestInterval(): Interval {
    return this.intervals.sort((a, b) => this.convertIntervalToMinutes(a) - this.convertIntervalToMinutes(b))[0]!;
  }

  // --- Time Utility Methods (from your original code) ---
  private getAlignedBarTime(now: Date, marketStart: Date, interval: Interval) {
    const intervalMinutes = this.convertIntervalToMinutes(interval);
    const minutesSinceOpen = Math.floor((now.getTime() - marketStart.getTime()) / 60000);
    const completedIntervals = Math.floor(minutesSinceOpen / intervalMinutes);

    const barStart = new Date(marketStart.getTime() + completedIntervals * intervalMinutes * 60000);
    const barEnd = new Date(barStart.getTime() + intervalMinutes * 60000);

    return { barStart, barEnd };
  }

  private convertIntervalToMinutes(interval: Interval): number {
    const mapping: Record<Interval, number> = {
      "1m": 1, "5m": 5, "10m": 10,
      "15m": 15, "30m": 30, "60m": 60,
      "1d": 24 * 60, "1w": 7 * 24 * 60,
    };
    return mapping[interval];
  }

  private getCurrentTime(timezone: string): Date {
    return new Date(new Date().toLocaleString("en-US", { timeZone: timezone }));
  }
}
