import { BrokerError } from "../errors/BrokerError";

export async function withRetry<T>(
  fn: () => Promise<T>,
  options: { retries?: number; delayMs?: number; factor?: number } = {}
): Promise<T> {
  const { retries = 3, delayMs = 500, factor = 2 } = options;

  let attempt = 0;
  let lastError: any;

  while (attempt <= retries) {
    try {
      return await fn();
    } catch (err: any) {
      lastError = err;
      const wait = delayMs * Math.pow(factor, attempt);
      if (attempt >= retries) break;
      if (err.isRetryable || [429, 500, 502, 503, 504].includes(err.code)) {
        await new Promise((res) => setTimeout(res, wait));
        attempt++;
        continue;
      }
      break;
    }
  }

  throw new BrokerError(`Failed after ${retries + 1} attempts: ${lastError?.message}`, {
    code: lastError?.code,
    details: lastError,
    isRetryable: false,
  });
}
