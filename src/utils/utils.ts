import { Quote } from "../models";
import fs from 'fs';
import path from 'path';

export function mapTickToQuote(t: any): Quote {
  return {
    symbol: t.instrument_token.toString(),
    lastPrice: t.last_price,
    change: t.change || 0,
    percentChange: t.change || 0,
    ohlc: t.ohlc
      ? {
          symbol: t.instrument_token.toString(),
          open: t.ohlc.open,
          high: t.ohlc.high,
          low: t.ohlc.low,
          close: t.ohlc.close,
          timestamp: new Date(),
        }
      : undefined,
    volume: t.volume,
    raw: t,
  };
}


export function setKeyInFile(filePath: string, key: string, value: string) {
  const envPath = path.resolve(filePath);

  // Read the file (or create if not exists)
  let envContent = '';
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf-8');
  }

  const keyRegex = new RegExp(`^${key}=.*$`, 'm');

  if (envContent.match(keyRegex)) {
    // Replace existing key
    envContent = envContent.replace(keyRegex, `${key}=${value}`);
  } else {
    // Add new key
    if (envContent.length && !envContent.endsWith('\n')) envContent += '\n';
    envContent += `${key}=${value}\n`;
  }

  fs.writeFileSync(envPath, envContent, 'utf-8');
  console.log(`Set ${key}=${value} in ${filePath}`);
}