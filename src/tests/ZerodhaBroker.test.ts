
// import { <PERSON>dhaBroker } from "../broker/ZerodhaBroker";
// import { Op<PERSON><PERSON><PERSON><PERSON> } from "../models";

// // Mock KiteConnect
// jest.mock("kiteconnect", () => {
//   const mockInstruments = [
//     {
//       instrument_token: 12345,
//       tradingsymbol: "NIFTY25SEP24000CE",
//       exchange: "NFO",
//       name: "NIFTY",
//       segment: "NFO-OPT",
//       lot_size: 50,
//       tick_size: 0.05,
//       instrument_type: "CE",
//       expiry: "2025-09-25",
//       strike: 24000,
//     },
//     {
//       instrument_token: 12346,
//       tradingsymbol: "NIFTY25SEP24000PE",
//       exchange: "NFO",
//       name: "NIFTY",
//       segment: "NFO-OPT",
//       lot_size: 50,
//       tick_size: 0.05,
//       instrument_type: "PE",
//       expiry: "2025-09-25",
//       strike: 24000,
//     },
//   ];

//   return {
//     __esModule: true,
//     default: jest.fn().mockImplementation(() => ({
//       getInstruments: jest.fn().mockResolvedValue(mockInstruments),
//       getProfile: jest.fn().mockResolvedValue({
//         user_id: "AB1234",
//         user_name: "John Doe",
//       }),
//     })),
//     KiteTicker: jest.fn(),
//   };
// });

// describe("ZerodhaBroker", () => {
//   let broker: ZerodhaBroker;

//   beforeEach(async () => {
//     broker = new ZerodhaBroker("dummyKey", "dummySecret");
//     await broker.init(); // load instruments into memory
//   });

//   it("should load instruments on init", async () => {
//     const instruments = await broker.getInstruments("NFO");
//     expect(instruments.length).toBeGreaterThan(0);
//     expect(instruments[0]).toBeDefined();
//     expect(instruments[0]!.symbol).toBe("NIFTY25SEP24000CE");
//   });

//   it("should fetch profile", async () => {
//     const profile = await broker.getProfile();
//     expect(profile.userId).toBe("AB1234");
//     expect(profile.userName).toBe("John Doe");
//     expect(profile.broker).toBe("ZERODHA");
//   });

//   it("should return option chain for given symbol and expiry", async () => {
//     const expiry = new Date("2025-09-25");
//     const chain: OptionChain = await broker.getOptionChain("NIFTY", expiry);

//     expect(chain.underlying).toBe("NIFTY");
//     expect(chain.expiry.toISOString().split("T")[0]).toBe("2025-09-25");
//     expect(chain.contracts).toHaveLength(2);

//     const ce = chain.contracts.find((c) => c.optionType === "CE");
//     expect(ce?.strike).toBe(24000);
//   });

//   it("should throw error if instruments not initialized before option chain", async () => {
//     const uninitBroker = new ZerodhaBroker("dummy", "dummy");
//     await expect(
//       uninitBroker.getOptionChain("NIFTY", new Date("2025-09-25"))
//     ).rejects.toThrow("Instruments not loaded. Call init() first.");
//   });
// });

describe("ZerodhaBroker", () => {
  it("should be defined", () => {
    expect(true).toBe(true);
  });
});
