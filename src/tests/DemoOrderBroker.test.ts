import { DemoOrderBroker } from "../broker/DemoOrderBroker";

describe("DemoOrderBroker Position Management", () => {
  let broker: DemoOrderBroker;

  beforeEach(() => {
    broker = new DemoOrderBroker();
  });

  test("should handle short selling (SELL first) correctly", async () => {
    // Place a SELL order first (short selling)
    const sellOrder = await broker.placeOrder({
      tradingsymbol: "RELIANCE",
      exchange: "NSE",
      transactionType: "SELL",
      orderType: "MARKET",
      quantity: 100,
      product: "MIS",
    });

    expect(sellOrder.transactionType).toBe("SELL");
    expect(sellOrder.quantity).toBe(100);
    expect(sellOrder.status).toBe("COMPLETE");

    // Check that a short position was created
    const positions = await broker.getPositions();
    expect(positions).toHaveLength(1);
    expect(positions[0]!.symbol).toBe("RELIANCE");
    expect(positions[0]!.quantity).toBe(-100); // Negative quantity for short position
    expect(positions[0]!.avgPrice).toBe(sellOrder.avgPrice);
  });

  test("should handle covering short position with BUY order", async () => {
    // First, create a short position
    await broker.placeOrder({
      tradingsymbol: "RELIANCE",
      exchange: "NSE",
      transactionType: "SELL",
      orderType: "MARKET",
      quantity: 100,
      product: "MIS",
    });

    // Verify short position exists
    let positions = await broker.getPositions();
    expect(positions).toHaveLength(1);
    expect(positions[0]!.quantity).toBe(-100);

    // Now cover the short position with a BUY order
    await broker.placeOrder({
      tradingsymbol: "RELIANCE",
      exchange: "NSE",
      transactionType: "BUY",
      orderType: "MARKET",
      quantity: 100,
      product: "MIS",
    });

    // Position should be closed (removed)
    positions = await broker.getPositions();
    expect(positions).toHaveLength(0);
  });

  test("should handle partial covering of short position", async () => {
    // Create a short position of 100 shares
    await broker.placeOrder({
      tradingsymbol: "RELIANCE",
      exchange: "NSE",
      transactionType: "SELL",
      orderType: "MARKET",
      quantity: 100,
      product: "MIS",
    });

    // Partially cover with 60 shares
    await broker.placeOrder({
      tradingsymbol: "RELIANCE",
      exchange: "NSE",
      transactionType: "BUY",
      orderType: "MARKET",
      quantity: 60,
      product: "MIS",
    });

    // Should have remaining short position of 40 shares
    const positions = await broker.getPositions();
    expect(positions).toHaveLength(1);
    expect(positions[0]!.quantity).toBe(-40);
  });

  test("should handle traditional BUY first scenario", async () => {
    // Traditional scenario: BUY first
    await broker.placeOrder({
      tradingsymbol: "RELIANCE",
      exchange: "NSE",
      transactionType: "BUY",
      orderType: "MARKET",
      quantity: 50,
      product: "MIS",
    });

    // Check long position
    let positions = await broker.getPositions();
    expect(positions).toHaveLength(1);
    expect(positions[0]!.quantity).toBe(50); // Positive quantity for long position

    // Sell to close
    await broker.placeOrder({
      tradingsymbol: "RELIANCE",
      exchange: "NSE",
      transactionType: "SELL",
      orderType: "MARKET",
      quantity: 50,
      product: "MIS",
    });

    // Position should be closed
    positions = await broker.getPositions();
    expect(positions).toHaveLength(0);
  });
});
