import { ZerodhaBroker } from "../broker/ZerodhaBroker";

describe("ZerodhaBroker Integration", () => {
  let broker: ZerodhaBroker;

  beforeAll(async () => {
    const apiKey = process.env.ZERODHA_API_KEY!;
    const apiSecret = process.env.ZERODHA_API_SECRET!;
    const requestToken = process.env.ZERODHA_REQUEST_TOKEN;
    const accessToken = process.env.ZERODHA_ACCESS_TOKEN;

    broker = new ZerodhaBroker(apiKey, apiSecret, { debug: false });

    if (requestToken && !accessToken) {
      // Generate fresh access token
      await broker.generateSession(requestToken, apiSecret);
    } else {
      // If access token already stored
      broker.setAccessToken(process.env.ZERODHA_ACCESS_TOKEN!);
    }

    await broker.init();

    expect(await broker.isHealthy()).toBe(true);
  });

  it("should fetch profile", async () => {
    const profile = await broker.getProfile();
    expect(profile.userId).toBeDefined();
    expect(profile.broker).toBe("ZERODHA");
  });

  it("should load instruments", async () => {
    const instruments = await broker.getInstruments("NSE");

    expect(instruments.length).toBeGreaterThan(1000);
  });

  it("should fetch LTP for NIFTY and RELIANCE", async () => {
    const ltp = await broker.getLTP(["NSE:NIFTY 50", "NSE:RELIANCE"]);
    expect(ltp["NSE:RELIANCE"]).toBeDefined();
    expect(ltp["NSE:RELIANCE"]!.lastPrice).toBeGreaterThan(0);

  });

  it("should fetch OHLC for RELIANCE", async () => {
    const ohlc = await broker.getOHLC("NSE:RELIANCE");
    expect(ohlc.open).toBeGreaterThan(0);
    expect(ohlc.close).toBeGreaterThan(0);
  });

  it("should fetch historical data", async () => {
    const history = await broker.getHistoricalData({
      instrumentToken: 738561, // NIFTY 50 token
      interval: "1d",
      from: new Date("2025-01-01"),
      to: new Date("2025-01-15"),
    });

    expect(history.length).toBeGreaterThan(0);
    expect(history[0]).toBeDefined();
    expect(history[0]!.open).toBeGreaterThan(0);

  });

  it("should fetch option chain for NIFTY", async () => {
    const expiry = new Date("2025-09-30");
    const chain = await broker.getOptionChain("NIFTY", expiry);
    
    expect(chain.contracts.length).toBeGreaterThan(0);
    expect(chain.contracts[0]).toBeDefined();
    expect(chain.contracts[0]!.optionType).toMatch(/CE|PE/);
  });

  it("should fetch positions and holdings", async () => {
    const positions = await broker.getPositions();
    const holdings = await broker.getHoldings();

    expect(Array.isArray(positions)).toBe(true);
    expect(Array.isArray(holdings)).toBe(true);
  });

  it("should connect ticker and stream ticks", async () => {
    await new Promise(async (resolve) => {
      await broker.connectTicker({
        onTicks: (ticks) => {
          try {
            expect(ticks.length).toBeGreaterThan(0);
            expect(ticks[0]).toBeDefined();
            expect(ticks[0]!.lastPrice).toBeGreaterThan(0);
            broker.disconnectTicker();
            resolve(true);
          } catch (err) {
            console.error(err);
            resolve(false);
          }
        },
        onConnect: () => {
          broker.subscribeTicks([738561]);
        },
        onDisconnect: () => {
          resolve(false);
        },
        onError: (err) => {
          console.error(err);
          resolve(false);
        },
        reconnect: false,
      });
    });
  });

});
