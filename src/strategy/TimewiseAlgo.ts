import { format, toZonedTime } from "date-fns-tz";
import { Algo, AlgoOptions } from "../algo";
import { DemoOrderBroker } from "../broker/DemoOrderBroker";
import { IBroker, Interval } from "../interface";
import { OHLC, Order, Quote } from "../models";

/**
 * Timewise Strategy
 * - Buy/Sell when close price is greater than high or low of previous bar (Close Greater than High Or Low)
 * - Exit when close price is between high and low of ohlc candle (Close Between High And Low)
 * - Exit all positions at market close (15:15)
 */
export class TimewiseAlgo extends Algo {
  private ohlc: OHLC | null = null;
  private isBuy = false;
  private slAmount = 500;
  private targetAmount = 1000;

  constructor(broker: IBroker, options: AlgoOptions) {
    super(broker, options);
  }

  async onStart(): Promise<void> {
    console.log("[TimewiseAlgo] Starting up");
  }

  onTick(_tick: Quote): void { }

  async onBar(symbol: string, interval: Interval, bar: OHLC): Promise<void> {
    // Update broker with current price for realistic order execution
    if (this.broker instanceof DemoOrderBroker) {
      this.broker.updateCurrentPrice(symbol, bar.close);
    }

    // Only trade on 5-minute intervals
    if (interval !== "5m") return;

    const barTime = toZonedTime(bar.timestamp, this.broker.getTiming().timezone);

    // Debug: Log bar processing
    const dateStr = format(barTime, 'yyyy-MM-dd');
    const timeStr = `${barTime.getHours().toString().padStart(2, '0')}:${barTime.getMinutes().toString().padStart(2, '0')}`;

    if (timeStr === "09:15") {
      console.log(`[TimewiseAlgo] New day started: ${dateStr}`);
      this.ohlc = null;
    }

    if (barTime.getHours() === 9 && barTime.getMinutes() === 20) {
      this.ohlc = bar;
      return;
    }

    if (!this.ohlc) return;

    if (barTime.getHours() >= 15 && barTime.getMinutes() > 15) {
      return;
    }


    // Get current position
    const positions = await this.getPositions();
    const position = positions.find(p => p.symbol === symbol);
    const currentQty = position ? position.quantity : 0;

    const slPoint = currentQty > 0 ? (this.slAmount / currentQty) : 0;
    const currentProfit = currentQty > 0 ? ((bar.close - position!.avgPrice) * currentQty) : 0;

    // Trading Logic - Close Greater than High Or Low (Entry Signal)
    if ((bar.close > this.ohlc.high || bar.close < this.ohlc.low) && currentQty === 0) {
      console.log(`[TimewiseAlgo] Close Greater than High Or Low detected at ${timeStr} - Current position: ${currentQty}`);

      if (bar.close > this.ohlc.high) {
        console.log(`[TimewiseAlgo] Entry - Close Greater than High Or Low - BUY ${symbol} at ${bar.close}`);
        await this.placeOrder({
          tradingsymbol: symbol,
          exchange: "NSE",
          transactionType: "BUY",
          orderType: "MARKET",
          quantity: Math.round(this.slAmount / (bar.close - this.ohlc.low)),
          product: "MIS",
          price: bar.close, 
        });
        this.isBuy = true;
      }

      if (bar.close < this.ohlc.low) {
        console.log(`[TimewiseAlgo] Entry - Close Greater than High Or Low - SELL ${symbol} at ${bar.close}`);
        await this.placeOrder({
          tradingsymbol: symbol,
          exchange: "NSE",
          transactionType: "SELL",
          orderType: "MARKET",
          quantity: Math.round(this.slAmount / (this.ohlc.high - bar.close)),
          product: "MIS",
          price: bar.close,
        });
        this.isBuy = false;
      }
    }

    // Trading Logic - Close Greater than (OHLC Candle) High And Low (Exit Signal)
    if (bar.close <= this.ohlc.low && this.isBuy && currentQty > 0) {
      console.log(`[TimewiseAlgo] Close Between High And Low detected at ${timeStr} - Current position: ${currentQty}`);

      console.log(`[TimewiseAlgo] Exit - Close Between High And Low - SELL ${symbol} at ${bar.close}`);
      await this.placeOrder({
        tradingsymbol: symbol,
        exchange: "NSE",
        transactionType: "SELL",
        orderType: "MARKET",
        quantity: currentQty,
        product: "MIS",
        price: bar.close,
      });
      this.ohlc = null;
    } else

      if (bar.close >= this.ohlc.high && !this.isBuy && currentQty > 0) {
        console.log(`[TimewiseAlgo] Close Between High And Low detected at ${timeStr} - Current position: ${currentQty}`);

        console.log(`[TimewiseAlgo] Exit - Close Between High And Low - BUY ${symbol} at ${bar.close}`);
        await this.placeOrder({
          tradingsymbol: symbol,
          exchange: "NSE",
          transactionType: "BUY",
          orderType: "MARKET",
          quantity: currentQty,
          product: "MIS",
          price: bar.close,
        });
        this.ohlc = null;
      }

    // Market Close Exit - Exit all positions at 15:15 | Current Profit > Target Amount
    if (((barTime.getHours() === 15 && barTime.getMinutes() === 15) || currentProfit >= this.targetAmount) && currentQty > 0) {
      console.log(`[TimewiseAlgo] Market Close at ${timeStr} - Current position: ${currentQty}`);

      console.log(`[TimewiseAlgo] Market Close - ${this.isBuy ? "SELL" : "BUY"} ${symbol} at ${bar.close}`);
      await this.placeOrder({
        tradingsymbol: symbol,
        exchange: "NSE",
        transactionType: this.isBuy ? "SELL" : "BUY",
        orderType: "MARKET",
        quantity: currentQty,
        product: "MIS",
        price: bar.close,
      });
      this.ohlc = null;
      
      console.log(``);
      console.log(``);
      console.log(``);
      console.log(`-------------------`);
      console.log(``);
      console.log(``);
      console.log(``);
    }
  }

  onFill(order: Order): void {
    console.log(`[TimewiseAlgo] Order filled: ${order.transactionType} ${order.symbol} @ ${order.avgPrice}`);
  }

  async onEnd(): Promise<void> {
    if (this.broker instanceof DemoOrderBroker) {
      this.broker.printPerformanceReport();
    }

    console.log("[TimewiseAlgo] finished");
  }
}
