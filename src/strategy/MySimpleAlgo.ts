import { Algo, AlgoOptions } from "../algo";
import { simpleMovingAverage } from "../indicators";
import { IBroker, Interval } from "../interface";
import { OHLC, Order, Quote } from "../models";
import { DemoOrderBroker } from "../broker/DemoOrderBroker";

/**
 * Simple Moving Average Crossover Strategy
 * - Buy when fast MA crosses above slow MA (Golden Cross)
 * - Sell when fast MA crosses below slow MA (Death Cross)
 * - Exit all positions at market close (15:30)
 */
export class MySimpleAlgo extends Algo {
  private fastMA = 10;
  private slowMA = 20;

  constructor(broker: IBroker, options: AlgoOptions) {
    super(broker, options);
  }

  async onStart(): Promise<void> {
    console.log("[MySimpleAlgo] Starting up");
  }

  onTick(_tick: Quote): void { }

  async onBar(symbol: string, interval: Interval, bar: OHLC): Promise<void> {
    // Update broker with current price for realistic order execution
    if (this.broker instanceof DemoOrderBroker) {
      this.broker.updateCurrentPrice(symbol, bar.close);
    }

    // Only trade on 5-minute intervals
    if (interval !== "5m") return;

    // Get historical bars
    const bars = this.getBars(symbol, interval);

    // Skip if not enough data for indicators
    if (bars.length < this.slowMA) return;

    // Calculate moving averages
    const closePrices = bars.map(b => b.close);
    const fastMA = simpleMovingAverage(closePrices, this.fastMA);
    const slowMA = simpleMovingAverage(closePrices, this.slowMA);

    // Get current and previous MA values
    const currentFast = fastMA[fastMA.length - 1];
    const currentSlow = slowMA[slowMA.length - 1];
    const prevFast = fastMA[fastMA.length - 2];
    const prevSlow = slowMA[slowMA.length - 2];

    // Skip if invalid values
    if (!currentFast || !currentSlow || !prevFast || !prevSlow) return;

    // Debug: Log bar processing
    const barTime = new Date(bar.timestamp);
    const timeStr = `${barTime.getHours().toString().padStart(2, '0')}:${barTime.getMinutes().toString().padStart(2, '0')}`;

    // Get current position
    const positions = await this.getPositions();
    const position = positions.find(p => p.symbol === symbol);
    const currentQty = position ? position.quantity : 0;

    // Trading Logic - Golden Cross (Entry Signal)
    if (currentFast > currentSlow && prevFast <= prevSlow) {
      console.log(`[MySimpleAlgo] Golden Cross detected at ${timeStr} - Current position: ${currentQty}`);
      if (currentQty === 0) {
        console.log(`[MySimpleAlgo] Golden Cross - BUY ${symbol} at ${bar.close}`);
        await this.placeOrder({
          tradingsymbol: symbol,
          exchange: "NSE",
          transactionType: "BUY",
          orderType: "MARKET",
          quantity: 1,
          product: "MIS",
          price: bar.close,
        });
      } else {
        console.log(`[MySimpleAlgo] Golden Cross IGNORED - Already have position: ${currentQty} shares`);
      }
    }

    // Trading Logic - Death Cross (Exit Signal)
    if (currentFast < currentSlow && prevFast >= prevSlow) {
      if (currentQty > 0) {
        console.log(`[MySimpleAlgo] Death Cross detected at ${timeStr} - Current position: ${currentQty}`);

        console.log(`[MySimpleAlgo] Death Cross - SELL ${symbol} at ${bar.close}`);
        await this.placeOrder({
          tradingsymbol: symbol,
          exchange: "NSE",
          transactionType: "SELL",
          orderType: "MARKET",
          quantity: currentQty,
          product: "MIS",
          price: bar.close,
        });
      }
    }

    // Market Close Exit - Exit all positions at 15:15
    if (barTime.getHours() === 15 && barTime.getMinutes() === 15) {
      if (currentQty > 0) {
        console.log(`[MySimpleAlgo] Market Close at ${timeStr} - Current position: ${currentQty}`);
        
        console.log(`[MySimpleAlgo] Market Close - SELL ${symbol} at ${bar.close}`);
        await this.placeOrder({
          tradingsymbol: symbol,
          exchange: "NSE",
          transactionType: "SELL",
          orderType: "MARKET",
          quantity: currentQty,
          product: "MIS",
          price: bar.close,
        });
      }
    }
  }

  onFill(order: Order): void {
    console.log(`[MySimpleAlgo] Order filled: ${order.transactionType} ${order.symbol} @ ${order.avgPrice}`);
  }

  async onEnd(): Promise<void> {
    if (this.broker instanceof DemoOrderBroker) {
      this.broker.printPerformanceReport();
    }

    console.log("[MySimpleAlgo] finished");
  }
}
