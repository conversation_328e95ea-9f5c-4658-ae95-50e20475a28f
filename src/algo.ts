import { Blotter, BlotterMode, BlotterOptions } from "./bottler";
import { IBroker, Interval, PlaceOrderParams, Variety } from "./interface";
import { OHLC, Order, Position, Quote } from "./models";

export interface AlgoOptions {
  instruments: string[];
  intervals: Interval[];
  mode: BlotterMode;
  // Backtest options
  backtestFrom?: Date;
  backtestTo?: Date;
  // Live options
  preloadDays?: number;
  // General options
  barLookbackWindow?: number; // How many bars to keep in memory

  bottlerBroker?: IBroker;
}

export abstract class Algo {
  protected broker: IBroker;
  protected blotter: Blotter;
  protected options: AlgoOptions;
  private pendingPositionUpdates = new Map<string, number>(); // Track position changes during backtesting

  constructor(broker: IBroker, options: AlgoOptions) {
    this.broker = broker;
    this.options = options;

    // The Algo creates and manages its own Blotter instance.
    const blotterOptions: BlotterOptions = {
      instruments: options.instruments,
      intervals: options.intervals,
      mode: options.mode,
      preloadDays: options.preloadDays,
      barLookbackWindow: options.barLookbackWindow,
    };
    
    const brokerToPass = options.bottlerBroker || this.broker;
    this.blotter = new Blotter(brokerToPass, blotterOptions);

    // Wire up the blotter events to the Algo's abstract methods
    this.blotter.on("tick", (tick: Quote) => this.onTick(tick));
    this.blotter.on("bar", (symbol: string, interval: Interval, bar: OHLC) => this.onBar(symbol, interval, bar));
  }

  /**
   * Returns the broker instance used by the Algo.
   * This allows the strategy to access broker-specific methods if needed.
   */
  public getBroker(): IBroker {
    return this.broker;
  }

  /**
   * Starts the trading algorithm.
   * In 'live' mode, it connects to the live market data stream.
   * In 'backtest' mode, it runs the simulation over historical data.
   */
  public async run(): Promise<void> {
    await this.onStart();

    if (this.options.mode === "live") {
      console.log("Starting Algo in LIVE mode...");
      await this.blotter.start();
    } else {
      if (!this.options.backtestFrom || !this.options.backtestTo) {
        throw new Error("Backtest mode requires 'backtestFrom' and 'backtestTo' dates.");
      }
      console.log(`Starting Algo in BACKTEST mode from ${this.options.backtestFrom} to ${this.options.backtestTo}...`);
      await this.blotter.runBacktest(this.options.backtestFrom, this.options.backtestTo);
      await this.onEnd(); // Call onEnd when backtest is complete
    }
  }

  public stop(): Promise<void> | void {
    console.log("Stopping Algo...");
    this.blotter.stop();
  }

  // --- Abstract methods for strategy implementation ---
  // These MUST be implemented by any strategy that extends this class.

  /**
   * Called once when the algorithm starts, before any market data is received.
   * Use this to initialize indicators, set up variables, etc.
   */
  abstract onStart(): Promise<void> | void;

  /**
   * Called on every new tick for a subscribed instrument.
   * @param tick The latest tick data.
   */
  abstract onTick(tick: Quote): Promise<void> | void;

  /**
   * Called when a new bar is completed for a given symbol and interval.
   * This is typically where the main trading logic resides.
   * @param symbol The instrument symbol (e.g., "RELIANCE").
   * @param interval The interval of the bar (e.g., "5m").
   * @param bar The completed OHLC bar.
   */
  abstract onBar(symbol: string, interval: Interval, bar: OHLC): Promise<void> | void;

  /**
   * Called when an order is filled or updated.
   * @param order The updated order information.
   */
  abstract onFill(order: Order): Promise<void> | void;

  /**
   * Called at the end of a backtest session.
   * Use this to print performance metrics, generate reports, etc.
   */
  abstract onEnd(): Promise<void> | void;


  // --- Helper / Broker Passthrough Methods ---
  // These provide a clean API for the strategy to interact with the market.

  protected getBars(symbol: string, interval: Interval): OHLC[] {
    return this.blotter.getBars(symbol, interval);
  }

  protected async getPositions(): Promise<Position[]> {
    return this.broker.getPositions();
  }

  protected async placeOrder(params: PlaceOrderParams): Promise<Order> {
    console.log(`[Algo] Placing order: ${params.transactionType} ${params.quantity} of ${params.tradingsymbol}`);
    try {
        const order = await this.broker.placeOrder(params);
        // In a real scenario, you would monitor this order's status via websockets or polling
        // For simplicity, we assume it gets filled and call onFill.
        // A more robust system would handle order updates separately.
        this.onFill({ ...order, status: "COMPLETE" });
        return order;
    } catch (error) {
        console.error(`[Algo] Error placing order for ${params.tradingsymbol}:`, error);
        throw error;
    }
  }

  protected async cancelOrder(orderId: string, variety?: Variety): Promise<boolean> {
    console.log(`[Algo] Cancelling order: ${orderId}`);
    return this.broker.cancelOrder({ orderId, variety });
  }
}