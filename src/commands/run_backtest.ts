import { Command, Flags } from '@oclif/core';
import { ZerodhaBroker } from '../broker/ZerodhaBroker';
import { DemoOrderBroker } from '../broker/DemoOrderBroker';
import { AlgoOptions, Algo } from '../algo';
import { Interval } from '../interface';
import { toZonedTime, format } from 'date-fns-tz';
import * as fs from 'fs';
import * as path from 'path';

export default class RunBacktest extends Command {
  static override description = 'Run backtest for a strategy with specified parameters';

  static override examples = [
    '<%= config.bin %> <%= command.id %> --strategy TestAlgo --start-datetime "2025-09-01 09:15:00" --end-datetime "2025-09-01 15:30:00" --instruments NSE:RELIANCE --intervals 5m',
    '<%= config.bin %> <%= command.id %> --strategy MySimpleAlgo --start-datetime "2025-09-01 09:15:00" --end-datetime "2025-09-02 15:30:00" --instruments NSE:RELIANCE,NSE:TCS --intervals 5m,15m --preload-days 10 --bar-lookback-window 100',
  ];

  private availableStrategies: string[] = [];

  static override flags = {
    // Strategy configuration
    strategy: Flags.string({
      char: 's',
      description: 'Strategy name to run (dynamically loaded from strategy folder)',
      required: true,
    }),

    // Date range with time
    'start-datetime': Flags.string({
      description: 'Start date and time for backtest (YYYY-MM-DD HH:MM:SS format)',
      required: true,
    }),
    'end-datetime': Flags.string({
      description: 'End date and time for backtest (YYYY-MM-DD HH:MM:SS format)',
      required: true,
    }),

    // Market data configuration
    instruments: Flags.string({
      char: 'i',
      description: 'Comma-separated list of instruments (e.g., NSE:RELIANCE,NSE:TCS)',
      required: true,
    }),
    intervals: Flags.string({
      description: 'Comma-separated list of intervals (1m,5m,10m,15m,30m,60m,1d,1w)',
      required: true,
    }),

    // Optional parameters
    'preload-days': Flags.integer({
      description: 'Number of days to preload for context',
      default: 0,
    }),
    'bar-lookback-window': Flags.integer({
      description: 'Number of bars to keep in memory',
      default: 50,
    }),

    // Broker configuration
    'use-demo-broker': Flags.boolean({
      description: 'Use demo broker instead of real broker for order execution',
      default: true,
    }),

    // Output options
    verbose: Flags.boolean({
      char: 'v',
      description: 'Enable verbose logging',
      default: false,
    }),

    // Standard flags
    version: Flags.version({ char: 'V' }),
    help: Flags.help({ char: 'h' }),
  };

  async run(): Promise<void> {
    const { flags } = await this.parse(RunBacktest);

    try {
      // Load available strategies dynamically
      await this.loadAvailableStrategies();

      // Initialize brokers first to get timezone information
      const { realBroker, tradingBroker } = await this.initializeBrokers(flags['use-demo-broker'], flags.verbose);

      // Get broker's timezone for accurate datetime parsing
      const marketTiming = realBroker.getTiming();
      const brokerTimezone = marketTiming.timezone;

      // Parse and validate inputs using broker's timezone
      const startDate = this.parseDateTime(flags['start-datetime'], brokerTimezone);
      const endDate = this.parseDateTime(flags['end-datetime'], brokerTimezone);
      const instruments = this.parseInstruments(flags.instruments);
      const intervals = this.parseIntervals(flags.intervals);

      // Validate date range
      if (startDate >= endDate) {
        this.error('Start date-time must be before end date-time');
      }

      // Validate strategy exists
      if (!this.availableStrategies.includes(flags.strategy)) {
        this.error(`Unknown strategy: ${flags.strategy}. Available strategies: ${this.availableStrategies.join(', ')}`);
      }

      if (flags.verbose) {
        this.log(`🚀 Starting backtest for strategy: ${flags.strategy}`);
        this.log(`📅 Date range: ${format(startDate, 'yyyy-MM-dd HH:mm:ss zzz', { timeZone: brokerTimezone })} to ${format(endDate, 'yyyy-MM-dd HH:mm:ss zzz', { timeZone: brokerTimezone })}`);
        this.log(`📊 Instruments: ${instruments.join(', ')}`);
        this.log(`⏱️  Intervals: ${intervals.join(', ')}`);
        this.log(`🔄 Preload days: ${flags['preload-days']}`);
        this.log(`📈 Bar lookback window: ${flags['bar-lookback-window']}`);
        this.log(`🌍 Broker timezone: ${brokerTimezone}`);
      }

      // Create algorithm options
      const algoOptions: AlgoOptions = {
        instruments,
        intervals,
        mode: 'backtest',
        backtestFrom: startDate,
        backtestTo: endDate,
        preloadDays: flags['preload-days'],
        barLookbackWindow: flags['bar-lookback-window'],
        bottlerBroker: realBroker, // Use real broker for historical data
      };

      // Initialize and run strategy
      const strategy = await this.createStrategy(flags.strategy, tradingBroker, algoOptions);

      this.log('\n🎯 Running backtest...\n');

      const startTime = Date.now();
      await strategy.run();
      const endTime = Date.now();

      this.log(`\n✅ Backtest completed in ${(endTime - startTime) / 1000}s`);

      // Display results
      await this.displayResults(strategy, flags.strategy, flags.verbose);

    } catch (error) {
      this.error(`❌ Backtest failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async loadAvailableStrategies(): Promise<void> {
    try {
      const strategyDir = path.join(__dirname, '..', 'strategy');
      const files = fs.readdirSync(strategyDir);

      this.availableStrategies = files
        .filter(file => (file.endsWith('.ts') || file.endsWith('.js')) && !file.endsWith('.d.ts'))
        .map(file => path.basename(file, path.extname(file)))
        .filter(name => name !== 'index') // Exclude index files
        .filter((name, index, arr) => arr.indexOf(name) === index); // Remove duplicates
      
      if (this.availableStrategies.length === 0) {
        this.error('No strategies found in strategy folder');
      }
    } catch (error) {
      this.error(`Failed to load strategies: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private parseDateTime(dateTimeString: string, timezone: string): Date {
    // Parse date-time in format "YYYY-MM-DD HH:MM:SS"
    const dateTimeRegex = /^(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})$/;
    const match = dateTimeString.match(dateTimeRegex);

    if (!match) {
      this.error(`Invalid date-time format: ${dateTimeString}. Use YYYY-MM-DD HH:MM:SS format (e.g., "2025-09-01 09:15:00").`);
    }

    const [, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr] = match;
    const year = Number(yearStr);
    const month = Number(monthStr);
    const day = Number(dayStr);
    const hour = Number(hourStr);
    const minute = Number(minuteStr);
    const second = Number(secondStr);

    // Validate ranges
    if (month < 1 || month > 12 || day < 1 || day > 31 ||
        hour < 0 || hour > 23 || minute < 0 || minute > 59 || second < 0 || second > 59) {
      this.error(`Invalid date-time values: ${dateTimeString}. Check that month (1-12), day (1-31), hour (0-23), minute (0-59), and second (0-59) are valid.`);
    }

    // Create date treating the input as being in the broker's timezone
    // This means "2025-09-01 09:15:00" is interpreted as 9:15 AM in the broker's timezone
    const isoString = `${year.toString().padStart(4, '0')}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}T${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}`;

    // Parse as if it's in the broker's timezone
    const tempDate = new Date(isoString);
    if (isNaN(tempDate.getTime())) {
      this.error(`Invalid date-time: ${dateTimeString}. Please check the date is valid.`);
    }

    // Convert to the broker's timezone
    // This creates a Date object that represents the same wall-clock time in the broker's timezone
    const brokerDate = toZonedTime(tempDate, timezone);

    return brokerDate;
  }

  private parseInstruments(instrumentsString: string): string[] {
    return instrumentsString.split(',').map(s => s.trim()).filter(s => s.length > 0);
  }

  private parseIntervals(intervalsString: string): Interval[] {
    const validIntervals: Interval[] = ['1m', '5m', '10m', '15m', '30m', '60m', '1d', '1w'];
    const intervals = intervalsString.split(',').map(s => s.trim() as Interval);

    for (const interval of intervals) {
      if (!validIntervals.includes(interval)) {
        this.error(`Invalid interval: ${interval}. Valid intervals are: ${validIntervals.join(', ')}`);
      }
    }

    return intervals;
  }

  private async initializeBrokers(useDemoBroker: boolean, verbose: boolean) {
    let realBroker: ZerodhaBroker;
    let tradingBroker: ZerodhaBroker | DemoOrderBroker;

    // Always need a real broker for historical data
    const { ZERODHA_API_KEY, ZERODHA_API_SECRET, ZERODHA_ACCESS_TOKEN } = process.env;

    if (!ZERODHA_API_KEY || !ZERODHA_API_SECRET || !ZERODHA_ACCESS_TOKEN) {
      this.error('Missing Zerodha credentials. Please set ZERODHA_API_KEY, ZERODHA_API_SECRET, and ZERODHA_ACCESS_TOKEN environment variables.');
    }

    if (verbose) {
      this.log('🔌 Initializing Zerodha broker for historical data...');
    }

    realBroker = new ZerodhaBroker(ZERODHA_API_KEY, ZERODHA_API_SECRET, {
      accessToken: ZERODHA_ACCESS_TOKEN,
    });
    await realBroker.init();

    if (useDemoBroker) {
      if (verbose) {
        this.log('🎮 Using demo broker for order execution...');
      }
      tradingBroker = new DemoOrderBroker(realBroker);
    } else {
      if (verbose) {
        this.log('💰 Using real broker for order execution...');
      }
      tradingBroker = realBroker;
    }

    return { realBroker, tradingBroker };
  }

  private async createStrategy(strategyName: string, broker: ZerodhaBroker | DemoOrderBroker, options: AlgoOptions): Promise<Algo> {
    try {
      // Dynamically import the strategy
      const strategyPath = path.join(__dirname, '..', 'strategy', `${strategyName}.js`);
      const strategyModule = await import(strategyPath);

      // Get the strategy class (assuming it's the default export or named export matching the file name)
      const StrategyClass = strategyModule.default || strategyModule[strategyName];

      if (!StrategyClass) {
        this.error(`Strategy class not found in ${strategyName}. Expected default export or named export '${strategyName}'.`);
      }

      // Create and return the strategy instance
      return new StrategyClass(broker, options);
    } catch (error) {
      this.error(`Failed to load strategy '${strategyName}': ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async displayResults(strategy: Algo, strategyName: string, verbose: boolean) {
    this.log('\n📊 BACKTEST RESULTS');
    this.log('==================');

    // Check if strategy has TestAlgo-like properties for detailed reporting
    const hasDetailedReporting = 'barsReceived' in strategy && 'fillsReceived' in strategy;

    if (hasDetailedReporting) {
      const testStrategy = strategy as any; // Type assertion for TestAlgo-like properties

      this.log(`📈 Total bars received: ${testStrategy.barsReceived?.length || 0}`);
      this.log(`💼 Total orders filled: ${testStrategy.fillsReceived?.length || 0}`);

      if (verbose && testStrategy.barsReceived?.length > 0) {
        this.log('\n📊 Bar Summary:');
        const barsBySymbol = testStrategy.barsReceived.reduce((acc: Record<string, number>, bar: any) => {
          acc[bar.symbol] = (acc[bar.symbol] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

        for (const [symbol, count] of Object.entries(barsBySymbol)) {
          this.log(`  ${symbol}: ${count} bars`);
        }
      }

      if (testStrategy.fillsReceived?.length > 0) {
        this.log('\n💰 Order Execution Log:');
        this.log('=======================');
        for (const order of testStrategy.fillsReceived) {
          // TODO: Convert to broker's timezone
          this.log(`${format(toZonedTime(order.placedAt, strategy.getBroker().getTiming().timezone), 'yyyy-MM-dd HH:mm:ss') || 'N/A'} | ${order.transactionType} ${order.quantity} ${order.symbol} @ ₹${order.avgPrice} | Status: ${order.status}`);
        }

        // Calculate basic P&L if we have orders
        const pnl = this.calculatePnL(testStrategy.fillsReceived);
        this.log(`\n💵 Total P&L: ₹${pnl.toFixed(2)}`);
      }
    } else {
      this.log(`📊 Strategy '${strategyName}' completed successfully.`);
      this.log('💡 This strategy does not provide detailed reporting like TestAlgo.');
    }

    // Get final positions from broker
    if (strategy['broker'] && typeof strategy['broker'].getPositions === 'function') {
      try {
        const positions = await strategy['broker'].getPositions();
        if (positions.length > 0) {
          this.log('\n📋 Final Positions:');
          this.log('==================');
          for (const position of positions) {
            this.log(`${position.symbol}: ${position.quantity} shares @ ₹${position.avgPrice} | P&L: ₹${position.pnl || 0}`);
          }
        }
      } catch (error) {
        if (verbose) {
          this.log(`⚠️  Could not fetch positions: ${error instanceof Error ? error.message : String(error)}`);
        }
      }
    }

    this.log('\n✨ Backtest completed successfully!');
  }

  private calculatePnL(orders: any[]): number {
    let totalPnL = 0;
    const positions: Record<string, { quantity: number; avgPrice: number }> = {};

    for (const order of orders) {
      const symbol = order.symbol;
      const quantity = order.transactionType === 'BUY' ? order.quantity : -order.quantity;
      const price = order.avgPrice || order.price;

      if (!positions[symbol]) {
        positions[symbol] = { quantity: 0, avgPrice: 0 };
      }

      const currentPos = positions[symbol];
      const newQuantity = currentPos.quantity + quantity;

      if (currentPos.quantity === 0) {
        // Opening position
        currentPos.quantity = newQuantity;
        currentPos.avgPrice = price;
      } else if ((currentPos.quantity > 0 && quantity > 0) || (currentPos.quantity < 0 && quantity < 0)) {
        // Adding to position
        const totalValue = (currentPos.quantity * currentPos.avgPrice) + (quantity * price);
        currentPos.quantity = newQuantity;
        currentPos.avgPrice = totalValue / newQuantity;
      } else {
        // Closing position (partially or fully)
        const closedQuantity = Math.min(Math.abs(currentPos.quantity), Math.abs(quantity));
        const pnlPerShare = order.transactionType === 'SELL'
          ? price - currentPos.avgPrice
          : currentPos.avgPrice - price;

        totalPnL += pnlPerShare * closedQuantity;
        currentPos.quantity = newQuantity;

        if (newQuantity !== 0) {
          currentPos.avgPrice = price;
        }
      }
    }

    return totalPnL;
  }
}
