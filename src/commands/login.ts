import { Command, Flags, ux } from '@oclif/core';
import { ZerodhaBroker } from '../broker/ZerodhaBroker';
import {prompt} from 'enquirer'
import { fsync } from 'fs';
import { setKeyInFile } from '../utils';

export default class Login extends Command {
  static override description = 'Login to the Zerodha API';

  static override examples = [
    '<%= config.bin %> <%= command.id %>',
    '<%= config.bin %> <%= command.id %>',
  ];

  static override flags = {
    // add --version flag to show CLI version
    version: Flags.version({ char: 'v' }),
    // add --help flag to show CLI version
    help: Flags.help({ char: 'h' }),
  };

  async run(): Promise<void> {
    const { flags } = await this.parse(Login);

    const apiKey = process.env.ZERODHA_API_KEY;
    const apiSecret = process.env.ZERODHA_API_SECRET;

    if (!apiKey || !apiSecret) {
      this.error('ZERODHA_API_KEY and ZERODHA_API_SECRET must be set in environment');
    }

    const broker = new ZerodhaBroker(apiKey, apiSecret);

    const url = broker.getLoginUrl();

    this.log(`Open ${url} in browser and login`);
    this.log('After login, you will be redirected to a blank page.');
    this.log('Copy the request token from the URL and paste it below:');

    const data = await prompt<{ requestToken: string }>({
      type: 'input',
      message: 'Request Token:',
      name: 'requestToken',
    })

    await broker.generateSession(data.requestToken, apiSecret);

    const accessToken = broker.getAccessToken();

    if (!accessToken) {
      this.error('Failed to generate access token');
    }

    setKeyInFile('.env', 'ZERODHA_ACCESS_TOKEN', accessToken);

    this.log('Login successful!');
  }
}
