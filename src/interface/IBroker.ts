// IBroker.ts
import { Holding, Instrument, MarketTiming, OHLC, OptionChain, Order, Position, Quote } from "../models";
import { HistoricalDataParams, PlaceOrderParams, ModifyOrderParams, CancelOrderParams, Exchanges } from "./params";

export interface IBroker {
  // Auth
  getLoginUrl(redirectUri?: string): string;
  generateSession(requestToken: string, apiSecret: string): Promise<void>;
  setAccessToken(accessToken: string): void;

  // Account
  getProfile(): Promise<{ userId: string; userName: string; broker: string }>;

  // Market data
  getInstruments(exchange?: Exchanges): Instrument[];
  getLTP(symbols: string | string[]): Promise<Record<string, Quote>>;
  getQuote(symbols: string | string[]): Promise<Record<string, Quote>>;
  getOHLC(symbol: string | string[]): Promise<OHLC>;
  getHistoricalData(params: HistoricalDataParams): Promise<OHLC[]>;
  getTiming(): MarketTiming;

  // Option chain 🔥 (mandatory)
  getOptionChain(symbol: string, expiry: Date): Promise<OptionChain>;

  // Orders
  placeOrder(params: PlaceOrderParams): Promise<Order>;
  modifyOrder(params: ModifyOrderParams): Promise<Order>;
  cancelOrder(params: CancelOrderParams): Promise<boolean>;
  getOrders(): Promise<Order[]>;
  getOrderHistory(orderId: string): Promise<Order[]>;

  // Portfolio
  getPositions(): Promise<Position[]>;
  getHoldings(): Promise<Holding[]>;

  // 🔥 Realtime ticker
  connectTicker(data: {
    onTicks: (ticks: Quote[]) => void, 
    onConnect?: () => void, 
    onDisconnect?: () => void,
    onError?: (err: any) => void,
    reconnect?: boolean | undefined,
  }): Promise<void>;
  disconnectTicker(): void;
  subscribeTicks(symbols: Array<number>, cb?: (ticks: Quote[]) => void): void;
  unsubscribeTicks(symbols: Array<number>): void;
}
