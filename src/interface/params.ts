import { TransactionType } from "kiteconnect";

export type OrderType = "MARKET" | "LIMIT" | "SL" | "SL-M";
export type ProductType = "CNC" | "MIS" | "NRML";
export type Variety = "regular" | "amo" | "co" | "iceberg" | "auction";
export type Validity = "DAY" | "IOC";
export type Interval = "1m" | "5m" | "10m" | "15m" | "30m" | "60m" | "1d" | "1w";
export type Exchanges = "NSE" | "BSE" | "NFO" | "CDS" | "BCD" | "BFO" | "MCX";

export interface PlaceOrderParams {
  variety?: Variety;
  exchange: string;
  tradingsymbol: string;
  transactionType: TransactionType;
  quantity: number;
  product: ProductType;
  orderType: OrderType;
  price?: number;
  triggerPrice?: number;
  disclosedQuantity?: number;
  validity?: "DAY" | "IOC";
  tag?: string;
}

export interface ModifyOrderParams {
  variety?: Variety;
  orderId: string;
  quantity?: number;
  price?: number;
  triggerPrice?: number;
  validity?: "DAY" | "IOC";
}

export interface CancelOrderParams {
  variety?: Variety;
  orderId: string;
}

export interface HistoricalDataParams {
  instrumentToken: number | string;
  from: Date;
  to: Date;
  interval: "1m" | "5m" | "10m" | "15m" | "30m" | "60m" | "1d" | "1w";
  continuous?: boolean;
  oi?: boolean;
}
