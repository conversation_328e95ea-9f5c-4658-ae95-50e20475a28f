export type OrderStatus =
  | 'OPEN'
  | 'PENDING'
  | 'COMPLETE'
  | 'CANCELLED'
  | 'REJECTED'
  | 'EXPIRED';

export type TransactionType = 'BUY' | 'SELL';

export interface Order {
  id: string;
  symbol: string;
  exchange: string;
  quantity: number;
  filledQuantity: number;
  price: number;
  avgPrice: number;
  orderType: string; // MARKET, LIMIT, SL, SL-M
  product: string;   // CNC, MIS, NRML
  status: OrderStatus;
  transactionType: TransactionType;
  placedAt: Date;
  updatedAt?: Date;
  raw?: any; // original broker response
}